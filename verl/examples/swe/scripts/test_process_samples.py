#!/usr/bin/env python3
"""
Test script for the multiple samples processing functionality.
Creates synthetic test data to verify the processing logic.
"""

import json
import tempfile
import os
import shutil
from typing import List, Dict, Any

# Test the processing functions
def create_test_data() -> List[Dict[str, Any]]:
    """Create synthetic test data for processing."""
    test_data = []
    
    # Create test instances with varying quality samples
    for instance_id in range(5):
        instance_name = f"test_instance_{instance_id}"
        
        # Create multiple samples for each instance
        for sample_id in range(8):  # 8 samples per instance
            # Vary the generation quality
            if sample_id < 2:
                # High quality samples
                generation = f"""
<think>
This is a high-quality solution for {instance_name}.
I need to fix the bug by modifying the function.
</think>

<solution>
```python
### test_file.py
<<<<<<< SEARCH
def buggy_function():
    return "old_code"
=======
def buggy_function():
    return "fixed_code"
>>>>>>> REPLACE
```
</solution>
"""
            elif sample_id < 5:
                # Medium quality samples
                generation = f"""
<think>
This is a medium-quality solution for {instance_name}.
</think>

<solution>
```python
### test_file.py
<<<<<<< SEARCH
def buggy_function():
    return "old_code"
=======
def buggy_function():
    return "partially_fixed_code"
>>>>>>> REPLACE
```
</solution>
"""
            else:
                # Low quality or malformed samples
                generation = f"This is a low-quality response for {instance_name} without proper format."
            
            # Create sample data
            sample = {
                'instance_id': instance_name,
                'sample_id': sample_id,
                'generation': generation,
                'prompt': f"Fix the bug in {instance_name}",
                'patch': '''--- a/test_file.py
+++ b/test_file.py
@@ -1,2 +1,2 @@
 def buggy_function():
-    return "old_code"
+    return "fixed_code"
''',
                'relevant_file_contents': {
                    'test_file.py': '''def buggy_function():
    return "old_code"
'''
                },
                'num_samples': 8
            }
            
            test_data.append(sample)
    
    return test_data


def test_processing_functions():
    """Test the core processing functions."""
    print("Testing processing functions...")
    
    # Import the processing functions
    import sys
    sys.path.append(os.path.dirname(__file__))
    
    try:
        from process_multiple_samples import (
            group_samples_by_instance,
            compute_similarity_between_samples,
            compute_sample_performance,
            filter_diverse_samples,
            categorize_samples,
            compute_diversity_score
        )
        print("✓ Successfully imported processing functions")
    except ImportError as e:
        print(f"✗ Failed to import processing functions: {e}")
        return False
    
    # Create test data
    test_data = create_test_data()
    print(f"✓ Created {len(test_data)} test samples")
    
    # Test grouping
    grouped = group_samples_by_instance(test_data)
    print(f"✓ Grouped into {len(grouped)} instances")
    
    # Test similarity computation
    sample1 = test_data[0]['generation']
    sample2 = test_data[1]['generation']
    similarity = compute_similarity_between_samples(sample1, sample2)
    print(f"✓ Computed similarity: {similarity:.3f}")
    
    # Test performance computation
    performance = compute_sample_performance(test_data[0])
    print(f"✓ Computed performance: {performance:.3f}")
    
    # Test diversity filtering
    instance_samples = grouped[list(grouped.keys())[0]]
    diverse_samples = filter_diverse_samples(instance_samples, similarity_threshold=0.8)
    print(f"✓ Filtered to {len(diverse_samples)} diverse samples from {len(instance_samples)}")
    
    # Test categorization
    sft_samples, rl_samples = categorize_samples(diverse_samples, performance_threshold=0.5)
    print(f"✓ Categorized into {len(sft_samples)} SFT and {len(rl_samples)} RL samples")
    
    # Test diversity scoring
    diversity = compute_diversity_score(instance_samples)
    print(f"✓ Computed diversity score: {diversity:.3f}")
    
    return True


def test_full_pipeline():
    """Test the full processing pipeline."""
    print("\nTesting full pipeline...")
    
    # Create temporary files
    temp_dir = tempfile.mkdtemp()
    input_file = os.path.join(temp_dir, "test_input.jsonl")
    output_dir = os.path.join(temp_dir, "output")
    
    try:
        # Create test data and save to file
        test_data = create_test_data()
        with open(input_file, 'w') as f:
            for item in test_data:
                f.write(json.dumps(item) + '\n')
        
        print(f"✓ Created test input file: {input_file}")
        
        # Run the processing script
        import subprocess
        import sys
        
        script_path = os.path.join(os.path.dirname(__file__), "process_multiple_samples.py")
        cmd = [
            sys.executable, script_path,
            "--input_path", input_file,
            "--output_dir", output_dir,
            "--similarity_threshold", "0.8",
            "--performance_threshold", "0.3",  # Lower threshold for test data
            "--max_samples_per_instance", "3",
            "--save_analysis"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Processing script completed successfully")
            
            # Check output files
            sft_files = [f for f in os.listdir(output_dir) if f.startswith("sft_filtered_")]
            rl_files = [f for f in os.listdir(output_dir) if f.startswith("rl_filtered_")]
            analysis_files = [f for f in os.listdir(output_dir) if f == "analysis_results.jsonl"]
            
            print(f"✓ Generated {len(sft_files)} SFT file(s)")
            print(f"✓ Generated {len(rl_files)} RL file(s)")
            print(f"✓ Generated {len(analysis_files)} analysis file(s)")
            
            # Check file contents
            if sft_files:
                sft_path = os.path.join(output_dir, sft_files[0])
                with open(sft_path, 'r') as f:
                    sft_count = sum(1 for line in f)
                print(f"✓ SFT file contains {sft_count} samples")
            
            if rl_files:
                rl_path = os.path.join(output_dir, rl_files[0])
                with open(rl_path, 'r') as f:
                    rl_count = sum(1 for line in f)
                print(f"✓ RL file contains {rl_count} samples")
            
            return True
        else:
            print(f"✗ Processing script failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Pipeline test failed: {e}")
        return False
    finally:
        # Cleanup
        shutil.rmtree(temp_dir)
        print("✓ Cleaned up temporary files")


def main():
    """Run all tests."""
    print("=== Testing Multiple Samples Processing ===\n")
    
    # Test individual functions
    functions_ok = test_processing_functions()
    
    # Test full pipeline
    pipeline_ok = test_full_pipeline()
    
    print(f"\n=== Test Results ===")
    print(f"Functions test: {'PASS' if functions_ok else 'FAIL'}")
    print(f"Pipeline test: {'PASS' if pipeline_ok else 'FAIL'}")
    
    if functions_ok and pipeline_ok:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit(main())
