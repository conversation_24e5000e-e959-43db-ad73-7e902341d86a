# Multiple Sample Generation Script

This script uses the vLLM framework to generate multiple samples for each problem in the SWE-RL training dataset.

## Overview

The script reads from `data/swe_rl_train/rl_all_raw.jsonl` and generates multiple responses for each prompt using vLLM's efficient inference engine. It supports resuming from previous runs and batch processing for memory efficiency.

## Features

- **Multiple sampling**: Generate N samples per problem with configurable temperature and top-p
- **Resume capability**: Continue from where you left off if interrupted
- **Batch processing**: Process data in batches to manage memory usage
- **vLLM integration**: Uses vLLM for fast and efficient inference
- **Flexible configuration**: Extensive command-line options for customization

## Usage

### Basic Usage

```bash
python verl/examples/swe/scripts/generate_multiple_samples.py \
    --model_path "path/to/your/model" \
    --num_samples 5 \
    --temperature 0.8
```

### Using the Example Script

```bash
# Edit the parameters in the script first
./verl/examples/swe/scripts/run_generate_multiple_samples.sh
```

### Command Line Arguments

#### Required Arguments
- `--model_path`: Path to the model (local path or HuggingFace model name)

#### Data Arguments
- `--data_path`: Path to input JSONL file (default: `data/swe_rl_train/rl_all_raw.jsonl`)
- `--output_path`: Path to output JSONL file (default: `data/swe_rl_train/rl_all_raw_multiple_samples.jsonl`)
- `--max_examples`: Maximum number of examples to process (for testing)

#### Generation Arguments
- `--num_samples`: Number of samples to generate per prompt (default: 5)
- `--temperature`: Sampling temperature (default: 0.8)
- `--top_p`: Top-p sampling parameter (default: 0.95)
- `--max_tokens`: Maximum tokens to generate (default: 2048)
- `--seed`: Random seed for reproducibility (default: 42)

#### Model Arguments
- `--tensor_parallel_size`: Tensor parallel size for vLLM (default: 1)
- `--gpu_memory_utilization`: GPU memory utilization for vLLM (default: 0.9)
- `--dtype`: Data type for model weights (default: "auto")
- `--max_model_len`: Maximum model length/context size (default: auto-detected)

#### Processing Arguments
- `--batch_size`: Batch size for processing (default: 1)
- `--resume`: Resume from existing output file

## Output Format

The script generates a JSONL file where each line contains:

```json
{
    "instance_id": "problem_identifier",
    "prompt": "original_prompt",
    "original_generation": "original_response_if_available",
    "num_samples": 5,
    "sample_id": 0,
    "generation": "generated_response",
    "patch": "...",
    "test_patch": "...",
    "FAIL_TO_PASS": [...],
    "PASS_TO_PASS": [...],
    "FAIL_TO_FAIL": [...],
    "relevant_file_contents": "..."
}
```

Each problem generates `num_samples` entries with `sample_id` from 0 to `num_samples-1`.

## Examples

### Generate 10 samples with high temperature
```bash
python verl/examples/swe/scripts/generate_multiple_samples.py \
    --model_path "Qwen/Qwen2.5-14B-Instruct" \
    --num_samples 10 \
    --temperature 1.0 \
    --top_p 0.9
```

### Process only first 100 examples for testing
```bash
python verl/examples/swe/scripts/generate_multiple_samples.py \
    --model_path "data/ckpt/Qwen3-14B" \
    --max_examples 100 \
    --num_samples 3
```

### Resume interrupted generation
```bash
python verl/examples/swe/scripts/generate_multiple_samples.py \
    --model_path "data/ckpt/Qwen3-14B" \
    --resume
```

### Use multiple GPUs
```bash
python verl/examples/swe/scripts/generate_multiple_samples.py \
    --model_path "data/ckpt/Qwen3-14B" \
    --tensor_parallel_size 4 \
    --gpu_memory_utilization 0.8
```

## Performance Tips

1. **Batch Size**: Start with batch_size=1 and increase if you have enough memory
2. **GPU Memory**: Adjust `gpu_memory_utilization` based on your GPU memory
3. **Tensor Parallel**: Use multiple GPUs with `tensor_parallel_size` for large models
4. **Resume**: Always use `--resume` to continue interrupted runs
5. **Testing**: Use `--max_examples` to test with a small subset first

## Requirements

- vLLM
- transformers
- torch
- tqdm

## Notes

- The script automatically handles tokenization and model configuration
- Progress is saved incrementally, so you can safely interrupt and resume
- Memory usage scales with batch size and model size
- Generation time depends on model size, sequence length, and number of samples
