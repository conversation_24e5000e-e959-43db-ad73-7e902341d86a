#!/usr/bin/env python3
"""
Script to generate multiple samples for each problem using vLLM framework.
Reads from data/swe_rl_train/rl_all_raw.jsonl and generates multiple responses for each prompt.
"""

import json
import argparse
import os
from typing import List, Dict, Any
from tqdm import tqdm
from transformers import AutoTokenizer, AutoConfig

# vLLM imports
from vllm import LLM, SamplingParams


def load_data(data_path: str) -> List[Dict[str, Any]]:
    """Load data from JSONL file."""
    data = []
    with open(data_path, 'r', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading data"):
            data.append(json.loads(line.strip()))
    return data


def setup_vllm_model(model_path: str,
                     tensor_parallel_size: int = 1,
                     gpu_memory_utilization: float = 0.9,
                     dtype: str = "auto",
                     max_model_len: int = None,
                     seed: int = 42) -> LLM:
    """Initialize vLLM model."""
    print(f"Initializing vLLM model from: {model_path}")
    
    # Load tokenizer to get vocab size and other info
    tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
    config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
    
    # Set max_model_len if not provided
    if max_model_len is None:
        max_model_len = getattr(config, 'max_position_embeddings', 4096)
    
    llm = LLM(
        model=model_path,
        tensor_parallel_size=tensor_parallel_size,
        gpu_memory_utilization=gpu_memory_utilization,
        dtype=dtype,
        max_model_len=max_model_len,
        trust_remote_code=True,
        enforce_eager=True,  # Disable CUDA graphs for better compatibility
        disable_log_stats=True,
        seed=seed,
    )
    
    return llm, tokenizer


def generate_multiple_samples(llm: LLM, 
                            prompts: List[str],
                            num_samples: int = 5,
                            temperature: float = 0.8,
                            top_p: float = 0.95,
                            max_tokens: int = 2048,
                            **kwargs) -> List[List[str]]:
    """Generate multiple samples for each prompt using vLLM."""
    
    sampling_params = SamplingParams(
        n=num_samples,  # Number of samples per prompt
        temperature=temperature,
        top_p=top_p,
        max_tokens=max_tokens,
        stop=None,  # Add stop tokens if needed
        **kwargs
    )
    
    print(f"Generating {num_samples} samples for {len(prompts)} prompts...")
    
    # Generate responses
    outputs = llm.generate(prompts, sampling_params, use_tqdm=True)
    
    # Extract generated texts
    all_samples = []
    for output in outputs:
        samples = [completion.text for completion in output.outputs]
        all_samples.append(samples)
    
    return all_samples


def save_results(data: List[Dict[str, Any]],
                all_samples: List[List[str]],
                output_path: str,
                num_samples: int):
    """Save results to JSONL file."""

    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    with open(output_path, 'w', encoding='utf-8') as f:
        for item, samples in zip(data, all_samples):
            # Create base entry with original data
            base_entry = {
                'instance_id': item['instance_id'],
                'prompt': item['prompt'],
                'original_generation': item.get('generation', ''),
                'num_samples': num_samples,
            }

            # Add any additional fields from original data
            for key in ['patch', 'test_patch', 'FAIL_TO_PASS', 'PASS_TO_PASS', 'FAIL_TO_FAIL', 'relevant_file_contents']:
                if key in item:
                    base_entry[key] = item[key]

            # Save each sample as a separate entry
            for j, sample in enumerate(samples):
                entry = base_entry.copy()
                entry.update({
                    'sample_id': j,
                    'generation': sample,
                })
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')

    print(f"Results saved to: {output_path}")


def load_existing_results(output_path: str) -> Dict[str, int]:
    """Load existing results to support resuming."""
    if not os.path.exists(output_path):
        return {}

    processed = {}
    with open(output_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                instance_id = data['instance_id']
                sample_id = data['sample_id']
                if instance_id not in processed:
                    processed[instance_id] = 0
                processed[instance_id] = max(processed[instance_id], sample_id + 1)
            except (json.JSONDecodeError, KeyError):
                continue

    return processed


def append_results(data_item: Dict[str, Any],
                  samples: List[str],
                  output_path: str,
                  num_samples: int,
                  start_sample_id: int = 0):
    """Append results to existing file."""

    # Create base entry with original data
    base_entry = {
        'instance_id': data_item['instance_id'],
        'prompt': data_item['prompt'],
        'original_generation': data_item.get('generation', ''),
        'num_samples': num_samples,
    }

    # Add any additional fields from original data
    for key in ['patch', 'test_patch', 'FAIL_TO_PASS', 'PASS_TO_PASS', 'FAIL_TO_FAIL', 'relevant_file_contents']:
        if key in data_item:
            base_entry[key] = data_item[key]

    # Append each sample
    with open(output_path, 'a', encoding='utf-8') as f:
        for j, sample in enumerate(samples):
            entry = base_entry.copy()
            entry.update({
                'sample_id': start_sample_id + j,
                'generation': sample,
            })
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')


def main():
    parser = argparse.ArgumentParser(description="Generate multiple samples using vLLM")
    parser.add_argument("--model_path", type=str, required=True,
                       help="Path to the model (local path or HuggingFace model name)")
    parser.add_argument("--data_path", type=str,
                       default="data/swe_rl_train/rl_all_raw.jsonl",
                       help="Path to input JSONL file")
    parser.add_argument("--output_path", type=str,
                       default="data/swe_rl_train/rl_all_raw_multiple_samples.jsonl",
                       help="Path to output JSONL file")
    parser.add_argument("--num_samples", type=int, default=5,
                       help="Number of samples to generate per prompt")
    parser.add_argument("--max_examples", type=int, default=None,
                       help="Maximum number of examples to process (for testing)")
    parser.add_argument("--temperature", type=float, default=0.8,
                       help="Sampling temperature")
    parser.add_argument("--top_p", type=float, default=0.95,
                       help="Top-p sampling parameter")
    parser.add_argument("--max_tokens", type=int, default=2048,
                       help="Maximum tokens to generate")
    parser.add_argument("--tensor_parallel_size", type=int, default=1,
                       help="Tensor parallel size for vLLM")
    parser.add_argument("--gpu_memory_utilization", type=float, default=0.9,
                       help="GPU memory utilization for vLLM")
    parser.add_argument("--dtype", type=str, default="auto",
                       help="Data type for model weights")
    parser.add_argument("--max_model_len", type=int, default=None,
                       help="Maximum model length (context size)")
    parser.add_argument("--batch_size", type=int, default=1,
                       help="Batch size for processing")
    parser.add_argument("--resume", action="store_true",
                       help="Resume from existing output file")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed for reproducibility")

    args = parser.parse_args()
    
    # Load data
    print(f"Loading data from: {args.data_path}")
    data = load_data(args.data_path)

    if args.max_examples:
        data = data[:args.max_examples]
        print(f"Processing first {args.max_examples} examples")

    print(f"Loaded {len(data)} examples")

    # Check for existing results if resuming
    processed = {}
    if args.resume:
        processed = load_existing_results(args.output_path)
        print(f"Found existing results for {len(processed)} instances")

    # Filter data to process only unfinished items
    data_to_process = []
    for item in data:
        instance_id = item['instance_id']
        completed_samples = processed.get(instance_id, 0)
        if completed_samples < args.num_samples:
            data_to_process.append((item, completed_samples))

    if not data_to_process:
        print("All samples already generated!")
        return

    print(f"Processing {len(data_to_process)} instances")

    # Setup vLLM model
    llm, _ = setup_vllm_model(
        args.model_path,
        tensor_parallel_size=args.tensor_parallel_size,
        gpu_memory_utilization=args.gpu_memory_utilization,
        dtype=args.dtype,
        max_model_len=args.max_model_len,
        seed=args.seed
    )

    # Process in batches
    total_generated = 0
    for i in tqdm(range(0, len(data_to_process), args.batch_size), desc="Processing batches"):
        batch_data = data_to_process[i:i + args.batch_size]

        # Prepare batch
        batch_prompts = []
        batch_items = []
        batch_remaining_samples = []

        for item, completed_samples in batch_data:
            remaining = args.num_samples - completed_samples
            if remaining > 0:
                batch_prompts.append(item['prompt'])
                batch_items.append(item)
                batch_remaining_samples.append(remaining)

        if not batch_prompts:
            continue

        try:
            # Generate samples for this batch
            batch_samples = generate_multiple_samples(
                llm, batch_prompts,
                num_samples=max(batch_remaining_samples),  # Generate max needed
                temperature=args.temperature,
                top_p=args.top_p,
                max_tokens=args.max_tokens,
                seed=args.seed
            )

            # Save results for each item in batch
            for item, samples, needed in zip(batch_items, batch_samples, batch_remaining_samples):
                # Take only the needed number of samples
                samples_to_save = samples[:needed]
                completed_samples = processed.get(item['instance_id'], 0)

                append_results(item, samples_to_save, args.output_path,
                             args.num_samples, start_sample_id=completed_samples)

                total_generated += len(samples_to_save)

                # Update processed count
                processed[item['instance_id']] = completed_samples + len(samples_to_save)

        except Exception as e:
            print(f"Error processing batch {i//args.batch_size + 1}: {e}")
            continue

    print(f"Generated {total_generated} new samples")
    print(f"Processing complete!")


if __name__ == "__main__":
    main()
