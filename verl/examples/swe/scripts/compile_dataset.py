import jsonlines
import random
import os

def read_jsonl(file_path, ratio=1):
    data = []
    with jsonlines.open(file_path, 'r') as reader:
        for obj in reader:
            # print(obj['generation'])
            # exit()
            
            if 'question' in obj:
                # update this key to 'prompt'
                obj['prompt'] = obj.pop('question')
            if 'answer' in obj:
                # update this key to 'generation'
                obj['generation'] = obj.pop('answer')
            data.append(obj)
    data = data[:int(len(data) * ratio)]
    return data

def write_jsonl(data, file_path):
    with jsonlines.open(file_path, 'w') as writer:
        for obj in data:
            writer.write(obj)

flag = False
if flag:
    # compile SFT datasets
    sft_path = [
        # "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation/sft_unique_2339.jsonl",
        # "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_orig/sft_unique_636.jsonl",
        # "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_smith/sft_unique_2920.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation/sft_16836.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_orig/sft_16806.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_smith/sft_18255.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_fixer/sft_multi_38494.jsonl"
        
    ]


    sft_output_path = [
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_sft_all_local0.5/swe_rl_sft_all_local0.5_QA_train_epoch_0.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_sft_all_local0.5/swe_rl_sft_all_local0.5_QA_train_epoch_1.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_sft_all_local0.5/swe_rl_sft_all_local0.5_QA_train_epoch_2.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_sft_all_local0.5/swe_rl_sft_all_local0.5_QA_train_epoch_3.jsonl"
    ]
    # read all sft data and combine them. Write the same data into the sft_output_path
    data = []
    for path in sft_path:
        data.extend(read_jsonl(path))

    # add the localization data
    # # "/lustre/fs1/portfolios/llmservice/projects/llmservice_fm_post/users/shengchiehl/datasets/swe_loc_16k/swe_loc_16k_QA_train_epoch_0.jsonl"
    data.extend(read_jsonl("/lustre/fs1/portfolios/llmservice/projects/llmservice_fm_post/users/shengchiehl/datasets/swe_loc_32k/swe_loc_32k_QA_train_epoch_0.jsonl", ratio=1))

    print(f"Total {len(data)} SFT data")

    for output_path in sft_output_path:
        # shuffle the dataset
        random.shuffle(data)
        write_jsonl(data, output_path)


    # conver the dataset
    os.system("python verl/examples/swe/scripts/convert.py --src-folder /lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_sft_all_local0.5/")

    # compile RL datasets


else:
    rl_path = [
        # "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation/rl_unique_9159.jsonl",
        # "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_orig/rl_unique_2796.jsonl",
        # "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_fixer/rl_40876.jsonl",
        "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/r1_data_distillation_smith/rl_3897.jsonl"
    ]
    
    rl_output_path = "/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/rl_smith_raw.jsonl"
    
    data = []
    for path in rl_path:
        data.extend(read_jsonl(path))
    
    print(f"Total {len(data)} RL data")
    write_jsonl(data, rl_output_path)
    

