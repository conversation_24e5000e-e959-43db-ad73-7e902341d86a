#!/bin/bash

# Example script to run multiple sample generation
# Modify the parameters according to your setup

# Model path - change this to your model path
MODEL_PATH="data/ckpt/Qwen3-14B"  # or use HuggingFace model name like "Qwen/Qwen2.5-14B-Instruct"

# Data paths
DATA_PATH="data/swe_rl_train/rl_all_raw.jsonl"
OUTPUT_PATH="data/swe_rl_train/rl_all_raw_multiple_samples.jsonl"

# Generation parameters
NUM_SAMPLES=8
TEMPERATURE=0.8
TOP_P=0.95
MAX_TOKENS=32768

# Model parameters
TENSOR_PARALLEL_SIZE=4
GPU_MEMORY_UTILIZATION=0.8
DTYPE="bfloat16"
MAX_MODEL_LEN=65536

# Processing parameters
BATCH_SIZE=1
# MAX_EXAMPLES=10  # Set to null or remove for processing all examples
SEED=42

echo "Starting multiple sample generation..."
echo "Model: $MODEL_PATH"
echo "Data: $DATA_PATH"



python verl/examples/swe/scripts/generate_multiple_samples.py \
    --model_path "$MODEL_PATH" \
    --data_path "$DATA_PATH" \
    --output_path "$OUTPUT_PATH" \
    --num_samples $NUM_SAMPLES \
    # --max_examples $MAX_EXAMPLES \
    --temperature $TEMPERATURE \
    --top_p $TOP_P \
    --max_tokens $MAX_TOKENS \
    --tensor_parallel_size $TENSOR_PARALLEL_SIZE \
    --gpu_memory_utilization $GPU_MEMORY_UTILIZATION \
    --dtype "$DTYPE" \
    --max_model_len $MAX_MODEL_LEN \
    --batch_size $BATCH_SIZE \
    --seed $SEED \
    --resume

echo "Generation complete!"
