#!/usr/bin/env python3
"""
Script to extract high-quality samples from merged multiple samples data.
Retains instances where the model achieves reasonable performance in at least one sample.
Selects the best performing sample for each qualifying instance.
Based on the logic from generate_data_split.py.
"""

import json
import argparse
import os
from typing import List, Dict, Any
from tqdm import tqdm

from collections import defaultdict
import numpy as np

# Import functions from generate_data_split.py
import sys
sys.path.append(os.path.dirname(__file__))
from generate_data_split import compute_score


def count_lines(file_path: str) -> int:
    """Count total lines in file for progress tracking."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return sum(1 for _ in f)


def process_data_in_batches(file_path: str,
                           performance_threshold: float = 0.5,
                           batch_size: int = 10000) -> Dict[str, Dict[str, Any]]:
    """
    Process data in batches to avoid memory issues.
    Returns a dictionary mapping instance_id to the best sample for that instance.
    """
    instance_best_samples = {}  # instance_id -> best_sample
    current_batch = defaultdict(list)  # instance_id -> list of samples

    total_lines = count_lines(file_path)
    print(f"Processing {total_lines} samples in batches of {batch_size}")

    with open(file_path, 'r', encoding='utf-8') as f:
        processed_lines = 0

        with tqdm(total=total_lines, desc="Processing batches") as pbar:
            for line in f:
                try:
                    item = json.loads(line.strip())
                    instance_id = item['instance_id']
                    current_batch[instance_id].append(item)
                    processed_lines += 1
                    pbar.update(1)

                    # Process batch when it gets large enough
                    if processed_lines % batch_size == 0:
                        process_batch(current_batch, instance_best_samples, performance_threshold)
                        current_batch.clear()

                except json.JSONDecodeError:
                    pbar.update(1)
                    continue

        # Process remaining items
        if current_batch:
            process_batch(current_batch, instance_best_samples, performance_threshold)

    return instance_best_samples


def process_batch(batch: Dict[str, List[Dict[str, Any]]],
                 best_samples: Dict[str, Dict[str, Any]],
                 performance_threshold: float):
    """Process a batch of samples and update best_samples."""
    for instance_id, samples in batch.items():
        # Get the best sample for this instance in this batch
        best_sample = select_best_sample(samples, performance_threshold)

        if best_sample is not None:
            # Compare with existing best sample for this instance
            if instance_id not in best_samples:
                best_samples[instance_id] = best_sample
            else:
                current_best_score = best_samples[instance_id].get('performance_score', -1.0)
                new_score = best_sample.get('performance_score', -1.0)
                if new_score > current_best_score:
                    best_samples[instance_id] = best_sample


def analyze_data_in_batches(file_path: str,
                           performance_threshold: float = 0.5,
                           batch_size: int = 10000):
    """Analyze data in batches to avoid memory issues."""
    total_instances = set()
    qualified_instances = set()
    total_samples = 0
    qualified_samples = [0]  # Use list to allow modification in nested function
    score_list = []

    current_batch = defaultdict(list)
    total_lines = count_lines(file_path)

    print(f"Analyzing {total_lines} samples in batches of {batch_size}")

    with open(file_path, 'r', encoding='utf-8') as f:
        processed_lines = 0

        with tqdm(total=total_lines, desc="Analyzing batches") as pbar:
            for line in f:
                try:
                    item = json.loads(line.strip())
                    instance_id = item['instance_id']
                    current_batch[instance_id].append(item)
                    total_samples += 1
                    processed_lines += 1
                    pbar.update(1)

                    # Process batch when it gets large enough
                    if processed_lines % batch_size == 0:
                        analyze_batch(current_batch, total_instances, qualified_instances,
                                    qualified_samples, score_list, performance_threshold)
                        current_batch.clear()

                except json.JSONDecodeError:
                    pbar.update(1)
                    continue

        # Process remaining items
        if current_batch:
            analyze_batch(current_batch, total_instances, qualified_instances,
                        qualified_samples, score_list, performance_threshold)

    # Print analysis results
    print(f"\n=== Analysis Results ===")
    print(f"Total instances: {len(total_instances)}")
    print(f"Qualified instances (>=1 sample meeting threshold): {len(qualified_instances)}")
    print(f"Unqualified instances: {len(total_instances) - len(qualified_instances)}")
    print(f"\nTotal samples: {total_samples}")
    print(f"Qualified samples (score >= {performance_threshold}): {qualified_samples[0]}")
    print(f"Qualification rate: {len(qualified_instances) / len(total_instances) * 100:.1f}% of instances")

    if score_list:
        valid_scores = [s for s in score_list if s >= 0]
        if valid_scores:
            print(f"\nScore Statistics:")
            print(f"  Mean score: {np.mean(valid_scores):.3f}")
            print(f"  Median score: {np.median(valid_scores):.3f}")
            print(f"  Max score: {np.max(valid_scores):.3f}")
            print(f"  Min score: {np.min(valid_scores):.3f}")


def analyze_batch(batch: Dict[str, List[Dict[str, Any]]],
                 total_instances: set,
                 qualified_instances: set,
                 qualified_samples: List[int],
                 score_list: List[float],
                 performance_threshold: float):
    """Analyze a batch of samples."""
    for instance_id, samples in tqdm(batch.items(), desc="Analyzing batch"):
        total_instances.add(instance_id)

        # Compute scores for samples in this batch for this instance
        instance_qualified = False
        for sample in samples:
            score = compute_sample_performance(sample)
            score_list.append(score)

            if score >= performance_threshold:
                qualified_samples[0] += 1
                instance_qualified = True

        if instance_qualified:
            qualified_instances.add(instance_id)








def compute_sample_performance(sample: Dict[str, Any]) -> float:
    """Compute performance score for a single sample."""
    try:
        generation = sample['generation']
        patch = sample.get('patch', '')
        relevant_file_contents = sample.get('relevant_file_contents', {})
        
        if not patch or not relevant_file_contents:
            return -1.0
        
        extra_info = {'relevant_file_contents': relevant_file_contents}
        score = compute_score(
            predict_str=generation,
            ground_truth=patch,
            extra_info=extra_info
        )
        return score
    except Exception as e:
        print(f"Error computing performance for sample: {e}")
        return -1.0


def compute_all_sample_scores(samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Compute performance scores for all samples.

    Args:
        samples: List of samples for the same instance

    Returns:
        List of samples with performance scores added
    """
    scored_samples = []
    for sample in samples:
        score = compute_sample_performance(sample)
        sample_copy = sample.copy()
        sample_copy['performance_score'] = score
        scored_samples.append(sample_copy)

    return scored_samples


def should_retain_instance(samples: List[Dict[str, Any]],
                          performance_threshold: float = 0.5) -> bool:
    """
    Determine whether to retain an instance based on sample performance scores.

    Args:
        samples: List of samples for the same instance
        performance_threshold: Minimum threshold for considering a sample as high-quality

    Returns:
        True if at least one sample meets the performance threshold, False otherwise
    """
    # Compute scores for all samples
    scored_samples = compute_all_sample_scores(samples)

    # Check if any sample meets the threshold
    for sample in scored_samples:
        if sample.get('performance_score', -1.0) >= performance_threshold:
            return True

    return False


def select_best_sample(samples: List[Dict[str, Any]],
                      performance_threshold: float = 0.5) -> Dict[str, Any]:
    """
    Select the best sample from a list of samples for the same instance.

    Args:
        samples: List of samples for the same instance
        performance_threshold: Minimum threshold for considering a sample as high-quality

    Returns:
        The best sample if any sample meets the threshold, None otherwise
    """
    # Compute scores for all samples
    scored_samples = compute_all_sample_scores(samples)

    # Filter samples that meet the performance threshold
    qualified_samples = [s for s in scored_samples if s.get('performance_score', -1.0) >= performance_threshold]

    if not qualified_samples:
        return None

    # Return the sample with the highest score
    best_sample = max(qualified_samples, key=lambda x: x.get('performance_score', -1.0))
    return best_sample


def process_instance_samples(samples: List[Dict[str, Any]],
                           performance_threshold: float = 0.5) -> Dict[str, Any]:
    """
    Process samples for a single instance and return the best high-quality sample.

    Returns:
        The best sample if any sample meets the performance threshold, None otherwise
    """
    # Select the best sample that meets the performance threshold
    best_sample = select_best_sample(samples, performance_threshold)

    return best_sample


def save_processed_data(high_quality_data: List[Dict[str, Any]],
                       output_dir: str):
    """Save processed high-quality data to file."""
    os.makedirs(output_dir, exist_ok=True)

    output_path = os.path.join(output_dir, f"high_quality_filtered_{len(high_quality_data)}.jsonl")

    # Save high-quality data
    with open(output_path, 'w', encoding='utf-8') as f:
        for item in high_quality_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

    print(f"Saved {len(high_quality_data)} high-quality samples to: {output_path}")








def main():
    parser = argparse.ArgumentParser(description="Extract high-quality samples where model achieves reasonable performance")
    parser.add_argument("--input_path", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl",
                       help="Path to merged multiple samples file")
    parser.add_argument("--output_dir", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/processed",
                       help="Output directory for processed data")
    parser.add_argument("--performance_threshold", type=float, default=0.5,
                       help="Minimum performance threshold for considering a sample as high-quality")
    parser.add_argument("--batch_size", type=int, default=10000,
                       help="Batch size for processing data")
    parser.add_argument("--analyze_only", action="store_true",
                       help="Only analyze score distribution without processing")

    args = parser.parse_args()
    
    if args.analyze_only:
        print("Analysis mode - processing data in batches to compute statistics...")
        # For analysis, we'll do a lightweight pass through the data
        analyze_data_in_batches(args.input_path, args.performance_threshold, args.batch_size)
        return

    print(f"Processing data from: {args.input_path}")
    print(f"Performance threshold: {args.performance_threshold}")
    print(f"Batch size: {args.batch_size}")

    # Process data in batches to find best samples
    best_samples = process_data_in_batches(
        args.input_path,
        performance_threshold=args.performance_threshold,
        batch_size=args.batch_size
    )

    print(f"\nProcessing complete!")
    print(f"Total high-quality samples: {len(best_samples)}")

    # Convert to list format for saving
    high_quality_data = []
    for instance_id, sample in best_samples.items():
        sample['selected_instance_id'] = instance_id
        high_quality_data.append(sample)

    # Save processed data
    save_processed_data(high_quality_data, args.output_dir)


if __name__ == "__main__":
    main()
