#!/usr/bin/env python3
"""
Script to process merged multiple samples data with similarity checking and performance filtering.
Based on the logic from generate_data_split.py.
"""

import json
import argparse
import os
from typing import List, Dict, Any, Tuple
from tqdm import tqdm
import difflib
from collections import defaultdict
import numpy as np

# Import functions from generate_data_split.py
import sys
sys.path.append(os.path.dirname(__file__))
from generate_data_split import (
    compute_score, 
    parse_search_replace, 
    parse_git_patch,
    customized_calculate_search_replace_reward
)


def load_merged_data(file_path: str) -> List[Dict[str, Any]]:
    """Load merged multiple samples data from JSONL file."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading merged data"):
            try:
                item = json.loads(line.strip())
                data.append(item)
            except json.JSONDecodeError:
                continue
    return data


def group_samples_by_instance(data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Group samples by instance_id."""
    grouped = defaultdict(list)
    for item in data:
        instance_id = item['instance_id']
        grouped[instance_id].append(item)
    return dict(grouped)


def compute_similarity_between_samples(sample1: str, sample2: str) -> float:
    """Compute similarity between two generated samples using SequenceMatcher."""
    return difflib.SequenceMatcher(None, sample1, sample2, autojunk=False).ratio()


def compute_sample_performance(sample: Dict[str, Any]) -> float:
    """Compute performance score for a single sample."""
    try:
        generation = sample['generation']
        patch = sample.get('patch', '')
        relevant_file_contents = sample.get('relevant_file_contents', {})
        
        if not patch or not relevant_file_contents:
            return -1.0
        
        extra_info = {'relevant_file_contents': relevant_file_contents}
        score = compute_score(
            predict_str=generation,
            ground_truth=patch,
            extra_info=extra_info
        )
        return score
    except Exception as e:
        print(f"Error computing performance for sample: {e}")
        return -1.0


def filter_diverse_samples(samples: List[Dict[str, Any]], 
                          similarity_threshold: float = 0.8,
                          max_samples: int = 5) -> List[Dict[str, Any]]:
    """
    Filter samples to keep diverse ones based on similarity threshold.
    
    Args:
        samples: List of samples for the same instance
        similarity_threshold: Threshold for considering samples as similar
        max_samples: Maximum number of samples to keep
    
    Returns:
        List of filtered diverse samples
    """
    if len(samples) <= 1:
        return samples
    
    # Compute performance scores for all samples
    sample_scores = []
    for sample in samples:
        score = compute_sample_performance(sample)
        sample_scores.append((sample, score))
    
    # Sort by performance score (descending)
    sample_scores.sort(key=lambda x: x[1], reverse=True)
    
    # Select diverse samples
    selected_samples = []
    
    for sample, score in sample_scores:
        if len(selected_samples) >= max_samples:
            break
        
        # Check if this sample is too similar to already selected ones
        is_diverse = True
        for selected_sample in selected_samples:
            similarity = compute_similarity_between_samples(
                sample['generation'], 
                selected_sample['generation']
            )
            if similarity > similarity_threshold:
                is_diverse = False
                break
        
        if is_diverse:
            sample['performance_score'] = score
            selected_samples.append(sample)
    
    return selected_samples


def categorize_samples(samples: List[Dict[str, Any]], 
                      performance_threshold: float = 0.5) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Categorize samples into high-performance (SFT) and medium-performance (RL) based on scores.
    
    Args:
        samples: List of samples for the same instance
        performance_threshold: Threshold for categorizing samples
    
    Returns:
        Tuple of (sft_samples, rl_samples)
    """
    sft_samples = []
    rl_samples = []
    
    for sample in samples:
        score = sample.get('performance_score', -1.0)
        if score >= performance_threshold:
            sft_samples.append(sample)
        elif score > 0:
            rl_samples.append(sample)
        # Samples with score <= 0 are discarded
    
    return sft_samples, rl_samples


def process_instance_samples(instance_id: str, 
                           samples: List[Dict[str, Any]],
                           similarity_threshold: float = 0.8,
                           performance_threshold: float = 0.5,
                           max_samples_per_instance: int = 5) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Process samples for a single instance.
    
    Returns:
        Tuple of (sft_samples, rl_samples)
    """
    # Filter for diverse samples
    diverse_samples = filter_diverse_samples(
        samples, 
        similarity_threshold=similarity_threshold,
        max_samples=max_samples_per_instance
    )
    
    # Categorize samples
    sft_samples, rl_samples = categorize_samples(
        diverse_samples, 
        performance_threshold=performance_threshold
    )
    
    return sft_samples, rl_samples


def save_processed_data(sft_data: List[Dict[str, Any]], 
                       rl_data: List[Dict[str, Any]], 
                       output_dir: str):
    """Save processed SFT and RL data to separate files."""
    os.makedirs(output_dir, exist_ok=True)
    
    sft_path = os.path.join(output_dir, f"sft_filtered_{len(sft_data)}.jsonl")
    rl_path = os.path.join(output_dir, f"rl_filtered_{len(rl_data)}.jsonl")
    
    # Save SFT data
    with open(sft_path, 'w', encoding='utf-8') as f:
        for item in sft_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    # Save RL data
    with open(rl_path, 'w', encoding='utf-8') as f:
        for item in rl_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"Saved {len(sft_data)} SFT samples to: {sft_path}")
    print(f"Saved {len(rl_data)} RL samples to: {rl_path}")


def compute_diversity_score(samples: List[Dict[str, Any]]) -> float:
    """Compute average pairwise diversity score for a list of samples."""
    if len(samples) <= 1:
        return 1.0

    total_similarity = 0.0
    count = 0

    for i in range(len(samples)):
        for j in range(i + 1, len(samples)):
            similarity = compute_similarity_between_samples(
                samples[i]['generation'],
                samples[j]['generation']
            )
            total_similarity += similarity
            count += 1

    avg_similarity = total_similarity / count if count > 0 else 0.0
    return 1.0 - avg_similarity  # Diversity is inverse of similarity


def analyze_score_distribution(grouped_data: Dict[str, List[Dict[str, Any]]],
                              performance_threshold: float = 0.5):
    """Analyze the distribution of performance scores."""
    print("\n=== Score Distribution Analysis ===")

    total_instances = len(grouped_data)
    high_performance_instances = 0
    medium_performance_instances = 0
    low_performance_instances = 0

    total_samples = 0
    high_performance_samples = 0
    medium_performance_samples = 0

    score_distribution = []
    diversity_scores = []

    for instance_id, samples in tqdm(grouped_data.items(), desc="Analyzing scores"):
        total_samples += len(samples)

        # Compute scores for all samples in this instance
        scores = []
        for sample in samples:
            score = compute_sample_performance(sample)
            scores.append(score)
            score_distribution.append(score)

        # Compute diversity score for this instance
        diversity = compute_diversity_score(samples)
        diversity_scores.append(diversity)

        max_score = max(scores) if scores else -1.0
        high_count = sum(1 for s in scores if s >= performance_threshold)
        medium_count = sum(1 for s in scores if 0 < s < performance_threshold)

        high_performance_samples += high_count
        medium_performance_samples += medium_count

        if high_count >= 2:  # At least 2 high-performance samples
            high_performance_instances += 1
        elif max_score > 0:  # At least one positive score
            medium_performance_instances += 1
        else:
            low_performance_instances += 1

    print(f"Total instances: {total_instances}")
    print(f"High-performance instances (>=2 good samples): {high_performance_instances}")
    print(f"Medium-performance instances (>=1 positive sample): {medium_performance_instances}")
    print(f"Low-performance instances (no positive samples): {low_performance_instances}")
    print(f"\nTotal samples: {total_samples}")
    print(f"High-performance samples (score >= {performance_threshold}): {high_performance_samples}")
    print(f"Medium-performance samples (0 < score < {performance_threshold}): {medium_performance_samples}")

    # Score statistics
    valid_scores = [s for s in score_distribution if s >= 0]
    if valid_scores:
        print(f"\nScore Statistics:")
        print(f"  Mean score: {np.mean(valid_scores):.3f}")
        print(f"  Median score: {np.median(valid_scores):.3f}")
        print(f"  Max score: {np.max(valid_scores):.3f}")
        print(f"  Min score: {np.min(valid_scores):.3f}")

    # Diversity statistics
    if diversity_scores:
        print(f"\nDiversity Statistics:")
        print(f"  Mean diversity: {np.mean(diversity_scores):.3f}")
        print(f"  Median diversity: {np.median(diversity_scores):.3f}")
        print(f"  Max diversity: {np.max(diversity_scores):.3f}")
        print(f"  Min diversity: {np.min(diversity_scores):.3f}")


def save_detailed_analysis(grouped_data: Dict[str, List[Dict[str, Any]]],
                          output_dir: str,
                          performance_threshold: float = 0.5):
    """Save detailed analysis results to a file."""
    analysis_path = os.path.join(output_dir, "analysis_results.jsonl")
    os.makedirs(output_dir, exist_ok=True)

    analysis_results = []

    for instance_id, samples in tqdm(grouped_data.items(), desc="Generating detailed analysis"):
        scores = []
        for sample in samples:
            score = compute_sample_performance(sample)
            scores.append(score)

        diversity = compute_diversity_score(samples)

        analysis_results.append({
            'instance_id': instance_id,
            'num_samples': len(samples),
            'scores': scores,
            'max_score': max(scores) if scores else -1.0,
            'mean_score': np.mean([s for s in scores if s >= 0]) if any(s >= 0 for s in scores) else -1.0,
            'high_performance_count': sum(1 for s in scores if s >= performance_threshold),
            'medium_performance_count': sum(1 for s in scores if 0 < s < performance_threshold),
            'diversity_score': diversity
        })

    with open(analysis_path, 'w', encoding='utf-8') as f:
        for result in analysis_results:
            f.write(json.dumps(result, ensure_ascii=False) + '\n')

    print(f"Detailed analysis saved to: {analysis_path}")


def main():
    parser = argparse.ArgumentParser(description="Process multiple samples with similarity and performance filtering")
    parser.add_argument("--input_path", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl",
                       help="Path to merged multiple samples file")
    parser.add_argument("--output_dir", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/processed",
                       help="Output directory for processed data")
    parser.add_argument("--similarity_threshold", type=float, default=0.8,
                       help="Similarity threshold for filtering diverse samples")
    parser.add_argument("--performance_threshold", type=float, default=0.5,
                       help="Performance threshold for SFT vs RL categorization")
    parser.add_argument("--max_samples_per_instance", type=int, default=5,
                       help="Maximum number of samples to keep per instance")
    parser.add_argument("--analyze_only", action="store_true",
                       help="Only analyze score distribution without processing")
    parser.add_argument("--save_analysis", action="store_true",
                       help="Save detailed analysis results to file")

    args = parser.parse_args()
    
    print(f"Loading data from: {args.input_path}")
    data = load_merged_data(args.input_path)
    print(f"Loaded {len(data)} total samples")
    
    # Group samples by instance
    grouped_data = group_samples_by_instance(data)
    print(f"Found {len(grouped_data)} unique instances")
    
    # Analyze score distribution
    analyze_score_distribution(grouped_data, args.performance_threshold)

    # Save detailed analysis if requested
    if args.save_analysis:
        save_detailed_analysis(grouped_data, args.output_dir, args.performance_threshold)

    if args.analyze_only:
        return
    
    # Process each instance
    all_sft_data = []
    all_rl_data = []
    
    print(f"\nProcessing instances with similarity_threshold={args.similarity_threshold}, "
          f"performance_threshold={args.performance_threshold}")
    
    for instance_id, samples in tqdm(grouped_data.items(), desc="Processing instances"):
        sft_samples, rl_samples = process_instance_samples(
            instance_id, samples,
            similarity_threshold=args.similarity_threshold,
            performance_threshold=args.performance_threshold,
            max_samples_per_instance=args.max_samples_per_instance
        )
        
        all_sft_data.extend(sft_samples)
        all_rl_data.extend(rl_samples)
    
    print(f"\nProcessing complete!")
    print(f"Total SFT samples: {len(all_sft_data)}")
    print(f"Total RL samples: {len(all_rl_data)}")
    
    # Save processed data
    save_processed_data(all_sft_data, all_rl_data, args.output_dir)


if __name__ == "__main__":
    main()
