#!/usr/bin/env python3
"""
Script to extract high-quality samples from merged multiple samples data.
Retains instances where the model achieves reasonable performance in at least one sample.
Selects the best performing sample for each qualifying instance.
Based on the logic from generate_data_split.py.
"""

import json
import argparse
import os
from typing import List, Dict, Any
from tqdm import tqdm

from collections import defaultdict
import numpy as np

# Import functions from generate_data_split.py
import sys
sys.path.append(os.path.dirname(__file__))
from generate_data_split import compute_score


def load_merged_data(file_path: str) -> List[Dict[str, Any]]:
    """Load merged multiple samples data from JSONL file."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in tqdm(f, desc="Loading merged data"):
            try:
                item = json.loads(line.strip())
                data.append(item)
            except json.JSONDecodeError:
                continue
    return data


def group_samples_by_instance(data: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Group samples by instance_id."""
    grouped = defaultdict(list)
    for item in data:
        instance_id = item['instance_id']
        grouped[instance_id].append(item)
    return dict(grouped)





def compute_sample_performance(sample: Dict[str, Any]) -> float:
    """Compute performance score for a single sample."""
    try:
        generation = sample['generation']
        patch = sample.get('patch', '')
        relevant_file_contents = sample.get('relevant_file_contents', {})
        
        if not patch or not relevant_file_contents:
            return -1.0
        
        extra_info = {'relevant_file_contents': relevant_file_contents}
        score = compute_score(
            predict_str=generation,
            ground_truth=patch,
            extra_info=extra_info
        )
        return score
    except Exception as e:
        print(f"Error computing performance for sample: {e}")
        return -1.0


def compute_all_sample_scores(samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Compute performance scores for all samples.

    Args:
        samples: List of samples for the same instance

    Returns:
        List of samples with performance scores added
    """
    scored_samples = []
    for sample in samples:
        score = compute_sample_performance(sample)
        sample_copy = sample.copy()
        sample_copy['performance_score'] = score
        scored_samples.append(sample_copy)

    return scored_samples


def should_retain_instance(samples: List[Dict[str, Any]],
                          performance_threshold: float = 0.5) -> bool:
    """
    Determine whether to retain an instance based on sample performance scores.

    Args:
        samples: List of samples for the same instance
        performance_threshold: Minimum threshold for considering a sample as high-quality

    Returns:
        True if at least one sample meets the performance threshold, False otherwise
    """
    # Compute scores for all samples
    scored_samples = compute_all_sample_scores(samples)

    # Check if any sample meets the threshold
    for sample in scored_samples:
        if sample.get('performance_score', -1.0) >= performance_threshold:
            return True

    return False


def select_best_sample(samples: List[Dict[str, Any]],
                      performance_threshold: float = 0.5) -> Dict[str, Any]:
    """
    Select the best sample from a list of samples for the same instance.

    Args:
        samples: List of samples for the same instance
        performance_threshold: Minimum threshold for considering a sample as high-quality

    Returns:
        The best sample if any sample meets the threshold, None otherwise
    """
    # Compute scores for all samples
    scored_samples = compute_all_sample_scores(samples)

    # Filter samples that meet the performance threshold
    qualified_samples = [s for s in scored_samples if s.get('performance_score', -1.0) >= performance_threshold]

    if not qualified_samples:
        return None

    # Return the sample with the highest score
    best_sample = max(qualified_samples, key=lambda x: x.get('performance_score', -1.0))
    return best_sample


def process_instance_samples(samples: List[Dict[str, Any]],
                           performance_threshold: float = 0.5) -> Dict[str, Any]:
    """
    Process samples for a single instance and return the best high-quality sample.

    Returns:
        The best sample if any sample meets the performance threshold, None otherwise
    """
    # Select the best sample that meets the performance threshold
    best_sample = select_best_sample(samples, performance_threshold)

    return best_sample


def save_processed_data(high_quality_data: List[Dict[str, Any]],
                       output_dir: str):
    """Save processed high-quality data to file."""
    os.makedirs(output_dir, exist_ok=True)

    output_path = os.path.join(output_dir, f"high_quality_filtered_{len(high_quality_data)}.jsonl")

    # Save high-quality data
    with open(output_path, 'w', encoding='utf-8') as f:
        for item in high_quality_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

    print(f"Saved {len(high_quality_data)} high-quality samples to: {output_path}")





def analyze_score_distribution(grouped_data: Dict[str, List[Dict[str, Any]]],
                              performance_threshold: float = 0.5):
    """Analyze the distribution of performance scores."""
    print("\n=== Score Distribution Analysis ===")

    total_instances = len(grouped_data)
    qualified_instances = 0  # Instances with at least one sample meeting threshold

    total_samples = 0
    qualified_samples = 0  # Samples meeting the performance threshold

    score_distribution = []

    for _, samples in tqdm(grouped_data.items(), desc="Analyzing scores"):
        total_samples += len(samples)

        # Compute scores for all samples in this instance
        scores = []
        for sample in samples:
            score = compute_sample_performance(sample)
            scores.append(score)
            score_distribution.append(score)

        qualified_count = sum(1 for s in scores if s >= performance_threshold)

        qualified_samples += qualified_count

        if qualified_count >= 1:  # At least one sample meeting threshold
            qualified_instances += 1

    print(f"Total instances: {total_instances}")
    print(f"Qualified instances (>=1 sample meeting threshold): {qualified_instances}")
    print(f"Unqualified instances: {total_instances - qualified_instances}")
    print(f"\nTotal samples: {total_samples}")
    print(f"Qualified samples (score >= {performance_threshold}): {qualified_samples}")
    print(f"Qualification rate: {qualified_instances / total_instances * 100:.1f}% of instances")

    # Score statistics
    valid_scores = [s for s in score_distribution if s >= 0]
    if valid_scores:
        print(f"\nScore Statistics:")
        print(f"  Mean score: {np.mean(valid_scores):.3f}")
        print(f"  Median score: {np.median(valid_scores):.3f}")
        print(f"  Max score: {np.max(valid_scores):.3f}")
        print(f"  Min score: {np.min(valid_scores):.3f}")


def save_detailed_analysis(grouped_data: Dict[str, List[Dict[str, Any]]],
                          output_dir: str,
                          performance_threshold: float = 0.5):
    """Save detailed analysis results to a file."""
    analysis_path = os.path.join(output_dir, "analysis_results.jsonl")
    os.makedirs(output_dir, exist_ok=True)

    analysis_results = []

    for instance_id, samples in tqdm(grouped_data.items(), desc="Generating detailed analysis"):
        scores = []
        for sample in samples:
            score = compute_sample_performance(sample)
            scores.append(score)

        analysis_results.append({
            'instance_id': instance_id,
            'num_samples': len(samples),
            'scores': scores,
            'max_score': max(scores) if scores else -1.0,
            'mean_score': np.mean([s for s in scores if s >= 0]) if any(s >= 0 for s in scores) else -1.0,
            'qualified_count': sum(1 for s in scores if s >= performance_threshold),
            'positive_count': sum(1 for s in scores if s > 0)
        })

    with open(analysis_path, 'w', encoding='utf-8') as f:
        for result in analysis_results:
            f.write(json.dumps(result, ensure_ascii=False) + '\n')

    print(f"Detailed analysis saved to: {analysis_path}")


def main():
    parser = argparse.ArgumentParser(description="Extract high-quality samples where model achieves reasonable performance")
    parser.add_argument("--input_path", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl",
                       help="Path to merged multiple samples file")
    parser.add_argument("--output_dir", type=str,
                       default="data/swe_rl_train/qwen_rl_sampling/processed",
                       help="Output directory for processed data")
    parser.add_argument("--performance_threshold", type=float, default=0.5,
                       help="Minimum performance threshold for considering a sample as high-quality")
    parser.add_argument("--analyze_only", action="store_true",
                       help="Only analyze score distribution without processing")
    parser.add_argument("--save_analysis", action="store_true",
                       help="Save detailed analysis results to file")

    args = parser.parse_args()
    
    print(f"Loading data from: {args.input_path}")
    data = load_merged_data(args.input_path)
    print(f"Loaded {len(data)} total samples")
    
    # Group samples by instance
    grouped_data = group_samples_by_instance(data)
    print(f"Found {len(grouped_data)} unique instances")
    
    # Analyze score distribution
    # analyze_score_distribution(grouped_data, args.performance_threshold)

    # Save detailed analysis if requested
    if args.save_analysis:
        save_detailed_analysis(grouped_data, args.output_dir, args.performance_threshold)

    if args.analyze_only:
        return
    
    # Process each instance to find high-quality samples
    high_quality_data = []

    print(f"\nProcessing instances with performance_threshold={args.performance_threshold}")

    for instance_id, samples in tqdm(grouped_data.items(), desc="Processing instances"):
        best_sample = process_instance_samples(
            samples,
            performance_threshold=args.performance_threshold
        )

        if best_sample is not None:
            # Add instance_id to the sample for tracking
            best_sample['selected_instance_id'] = instance_id
            high_quality_data.append(best_sample)

    print(f"\nProcessing complete!")
    print(f"Total high-quality samples: {len(high_quality_data)}")
    print(f"Success rate: {len(high_quality_data) / len(grouped_data) * 100:.1f}% of instances")

    # Save processed data
    save_processed_data(high_quality_data, args.output_dir)


if __name__ == "__main__":
    main()
