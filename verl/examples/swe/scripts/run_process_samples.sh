#!/bin/bash

# Script to extract high-quality samples where model achieves reasonable performance

# Input and output paths
INPUT_PATH="data/swe_rl_train/rl_all_raw_multiple_samples_merged.jsonl"
OUTPUT_DIR="data/swe_rl_train/qwen_rl_sampling/processed"

# Processing parameters
PERFORMANCE_THRESHOLD=0.5     # Minimum threshold for high-quality samples

echo "Extracting high-quality samples..."
echo "Input: $INPUT_PATH"
echo "Output directory: $OUTPUT_DIR"
echo "Performance threshold: $PERFORMANCE_THRESHOLD"

# Check if input file exists
if [ ! -f "$INPUT_PATH" ]; then
    echo "Error: Input file $INPUT_PATH does not exist!"
    echo "Please make sure the merged samples file is available."
    exit 1
fi

# First, run analysis only to see the score distribution
echo ""
echo "=== Running analysis to check score distribution ==="
python verl/examples/swe/scripts/process_multiple_samples.py \
    --input_path "$INPUT_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --performance_threshold $PERFORMANCE_THRESHOLD \
    --analyze_only \
    --save_analysis



# echo ""
# echo "=== Extracting high-quality samples ==="
# python verl/examples/swe/scripts/process_multiple_samples.py \
#     --input_path "$INPUT_PATH" \
#     --output_dir "$OUTPUT_DIR" \
#     --performance_threshold $PERFORMANCE_THRESHOLD

# echo ""
# echo "Processing complete!"
# echo "Check the output directory for high-quality data:"
# echo "  - High-quality samples: $OUTPUT_DIR/high_quality_filtered_*.jsonl"
