#!/bin/bash

# Script to process multiple samples with similarity and performance filtering

# Input and output paths
INPUT_PATH="data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl"
OUTPUT_DIR="data/swe_rl_train/qwen_rl_sampling/processed"

# Processing parameters
SIMILARITY_THRESHOLD=0.8      # Threshold for considering samples as similar
PERFORMANCE_THRESHOLD=0.5     # Threshold for SFT vs RL categorization
MAX_SAMPLES_PER_INSTANCE=5    # Maximum samples to keep per instance

echo "Processing multiple samples data..."
echo "Input: $INPUT_PATH"
echo "Output directory: $OUTPUT_DIR"
echo "Similarity threshold: $SIMILARITY_THRESHOLD"
echo "Performance threshold: $PERFORMANCE_THRESHOLD"
echo "Max samples per instance: $MAX_SAMPLES_PER_INSTANCE"

# Check if input file exists
if [ ! -f "$INPUT_PATH" ]; then
    echo "Error: Input file $INPUT_PATH does not exist!"
    echo "Please make sure the merged samples file is available."
    exit 1
fi

# First, run analysis only to see the score distribution
echo ""
echo "=== Running analysis to check score distribution ==="
python verl/examples/swe/scripts/process_multiple_samples.py \
    --input_path "$INPUT_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --similarity_threshold $SIMILARITY_THRESHOLD \
    --performance_threshold $PERFORMANCE_THRESHOLD \
    --max_samples_per_instance $MAX_SAMPLES_PER_INSTANCE \
    --analyze_only

echo ""
echo "=== Processing samples with filtering ==="
python verl/examples/swe/scripts/process_multiple_samples.py \
    --input_path "$INPUT_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --similarity_threshold $SIMILARITY_THRESHOLD \
    --performance_threshold $PERFORMANCE_THRESHOLD \
    --max_samples_per_instance $MAX_SAMPLES_PER_INSTANCE

echo ""
echo "Processing complete!"
echo "Check the output directory for filtered SFT and RL data:"
echo "  - SFT data: $OUTPUT_DIR/sft_filtered_*.jsonl"
echo "  - RL data: $OUTPUT_DIR/rl_filtered_*.jsonl"
