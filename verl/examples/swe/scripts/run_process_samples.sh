#!/bin/bash

# Script to extract high-quality samples where model achieves reasonable performance

# Input and output paths
INPUT_PATH="data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl"
OUTPUT_DIR="data/swe_rl_train/qwen_rl_sampling/processed"

# Processing parameters
SIMILARITY_THRESHOLD=0.8      # Threshold for considering samples as similar
PERFORMANCE_THRESHOLD=0.5     # Minimum threshold for high-quality samples
MAX_SAMPLES_PER_INSTANCE=5    # Maximum samples to consider per instance

echo "Extracting high-quality samples..."
echo "Input: $INPUT_PATH"
echo "Output directory: $OUTPUT_DIR"
echo "Similarity threshold: $SIMILARITY_THRESHOLD"
echo "Performance threshold: $PERFORMANCE_THRESHOLD"
echo "Max samples per instance: $MAX_SAMPLES_PER_INSTANCE"

# Check if input file exists
if [ ! -f "$INPUT_PATH" ]; then
    echo "Error: Input file $INPUT_PATH does not exist!"
    echo "Please make sure the merged samples file is available."
    exit 1
fi

# First, run analysis only to see the score distribution
echo ""
echo "=== Running analysis to check score distribution ==="
python verl/examples/swe/scripts/process_multiple_samples.py \
    --input_path "$INPUT_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --similarity_threshold $SIMILARITY_THRESHOLD \
    --performance_threshold $PERFORMANCE_THRESHOLD \
    --max_samples_per_instance $MAX_SAMPLES_PER_INSTANCE \
    --analyze_only \
    --save_analysis




# echo ""
# echo "=== Extracting high-quality samples ==="
# python verl/examples/swe/scripts/process_multiple_samples.py \
#     --input_path "$INPUT_PATH" \
#     --output_dir "$OUTPUT_DIR" \
#     --similarity_threshold $SIMILARITY_THRESHOLD \
#     --performance_threshold $PERFORMANCE_THRESHOLD \
#     --max_samples_per_instance $MAX_SAMPLES_PER_INSTANCE

# echo ""
# echo "Processing complete!"
# echo "Check the output directory for high-quality data:"
# echo "  - High-quality samples: $OUTPUT_DIR/high_quality_filtered_*.jsonl"
