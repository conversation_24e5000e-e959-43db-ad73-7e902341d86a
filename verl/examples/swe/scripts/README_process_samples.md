# Multiple Samples Processing Script

This script processes the merged multiple samples data to filter high-quality, diverse samples based on similarity checking and performance evaluation. It's based on the logic from `generate_data_split.py`.

## Overview

The script takes the merged multiple samples file (output from the generation pipeline) and:
1. **Groups samples by instance ID**
2. **Computes performance scores** for each sample using the SWE-RL reward function
3. **Filters for diversity** by removing highly similar samples
4. **Categorizes samples** into SFT (high-performance) and RL (medium-performance) datasets

## Features

- **Performance Evaluation**: Uses the same reward calculation as in `generate_data_split.py`
- **Similarity Filtering**: Removes redundant samples that are too similar to each other
- **Diversity Analysis**: Computes diversity scores for sample sets
- **Flexible Thresholds**: Configurable similarity and performance thresholds
- **Detailed Analytics**: Comprehensive analysis of score distributions and diversity metrics
- **Batch Processing**: Efficient processing of large datasets

## Usage

### Quick Start

```bash
# Run the processing script
./verl/examples/swe/scripts/run_process_samples.sh
```

### Manual Usage

```bash
# Basic processing
python verl/examples/swe/scripts/process_multiple_samples.py \
    --input_path "data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl" \
    --output_dir "data/swe_rl_train/qwen_rl_sampling/processed"

# Analysis only (no filtering)
python verl/examples/swe/scripts/process_multiple_samples.py \
    --input_path "data/swe_rl_train/qwen_rl_sampling/rl_all_raw_multiple_samples_merged.jsonl" \
    --analyze_only

# Custom thresholds
python verl/examples/swe/scripts/process_multiple_samples.py \
    --similarity_threshold 0.7 \
    --performance_threshold 0.6 \
    --max_samples_per_instance 3
```

## Command Line Arguments

### Required Arguments
- `--input_path`: Path to merged multiple samples JSONL file

### Optional Arguments
- `--output_dir`: Output directory for processed data (default: `data/swe_rl_train/qwen_rl_sampling/processed`)
- `--similarity_threshold`: Similarity threshold for filtering diverse samples (default: 0.8)
- `--performance_threshold`: Performance threshold for SFT vs RL categorization (default: 0.5)
- `--max_samples_per_instance`: Maximum number of samples to keep per instance (default: 5)
- `--analyze_only`: Only analyze score distribution without processing
- `--save_analysis`: Save detailed analysis results to file

## Processing Logic

### 1. Performance Scoring
Each sample is evaluated using the SWE-RL reward function:
- Parses the generated solution for search/replace blocks
- Applies the changes to the code context
- Computes similarity between predicted and oracle patches
- Returns a score between -1.0 and 1.0

### 2. Diversity Filtering
For each instance:
- Computes performance scores for all samples
- Sorts samples by performance (descending)
- Selects diverse samples by checking similarity with already selected ones
- Keeps samples that are sufficiently different (below similarity threshold)

### 3. Categorization
Samples are categorized based on performance scores:
- **SFT samples**: Score >= performance_threshold (default: 0.5)
- **RL samples**: 0 < Score < performance_threshold
- **Discarded**: Score <= 0

### 4. Diversity Scoring
Computes average pairwise diversity for sample sets:
- Diversity = 1.0 - average_similarity
- Higher diversity means more varied solutions

## Output Files

### Processed Data
- `sft_filtered_N.jsonl`: High-performance samples for SFT training
- `rl_filtered_N.jsonl`: Medium-performance samples for RL training

### Analysis Results (if `--save_analysis` is used)
- `analysis_results.jsonl`: Detailed per-instance analysis including:
  - Instance ID and sample count
  - Performance scores for all samples
  - Diversity metrics
  - Categorization counts

## Analysis Output

The script provides comprehensive analysis including:

### Score Distribution
- Total instances and samples
- High/medium/low performance instance counts
- Performance sample counts by category
- Score statistics (mean, median, min, max)

### Diversity Metrics
- Average diversity scores across instances
- Diversity distribution statistics

### Example Output
```
=== Score Distribution Analysis ===
Total instances: 1000
High-performance instances (>=2 good samples): 250
Medium-performance instances (>=1 positive sample): 400
Low-performance instances (no positive samples): 350

Total samples: 8000
High-performance samples (score >= 0.5): 800
Medium-performance samples (0 < score < 0.5): 2200

Score Statistics:
  Mean score: 0.234
  Median score: 0.156
  Max score: 0.987
  Min score: -1.000

Diversity Statistics:
  Mean diversity: 0.654
  Median diversity: 0.678
  Max diversity: 0.923
  Min diversity: 0.234
```

## Configuration Tips

### Similarity Threshold
- **0.9**: Very strict, keeps only highly diverse samples
- **0.8**: Balanced (default), good trade-off between diversity and quantity
- **0.7**: More lenient, keeps more samples but may include similar ones

### Performance Threshold
- **0.6**: Strict, only high-quality samples for SFT
- **0.5**: Balanced (default), reasonable quality bar
- **0.4**: Lenient, includes more samples but lower average quality

### Max Samples Per Instance
- **3**: Conservative, focuses on top samples
- **5**: Balanced (default), good diversity while maintaining quality
- **8**: Liberal, keeps more samples for diversity

## Integration with Training Pipeline

### SFT Training
Use the `sft_filtered_N.jsonl` file for supervised fine-tuning:
- Contains high-quality, diverse solutions
- Samples have performance scores >= threshold
- Ready for standard SFT training procedures

### RL Training
Use the `rl_filtered_N.jsonl` file for reinforcement learning:
- Contains medium-quality samples that can be improved
- Provides learning signal for RL algorithms
- Includes performance scores for reward modeling

## Performance Considerations

- **Memory Usage**: Scales with dataset size and number of samples per instance
- **Processing Time**: Depends on complexity of performance evaluation
- **Disk Space**: Output files are typically 10-30% of input size after filtering

## Requirements

- Python packages: `json`, `difflib`, `numpy`, `tqdm`
- Access to `generate_data_split.py` functions for performance evaluation
- Sufficient disk space for output files
