#!/usr/bin/env bash
# A multi-node Ray start-up script that does NOT rely on srun/scontrol *inside* the container.
# Instead, we parse $SLURM_NODELIST ourselves to figure out the node hostnames.
# source ~/.bashrc
# conda deactivate

unset PYTHONPATH

export PATH=/usr/bin:$PATH

# Debugging: Print SLURM variables
echo "SLURM_NODELIST: $SLURM_NODELIST"
echo "SLURM_NODEID: $SLURM_NODEID"

export NCCL_DEBUG=INFO
export NCCL_DEBUG_SUBSYS=ALL

# Function to expand SLURM_NODELIST without scontrol
expand_slurm_nodelist() {
    python - << END
import sys
import re

nodelist = """$SLURM_NODELIST"""

def expand_nodelist(nodelist):
    def parse_range(range_str):
        regex = r'([^\[]+)\[([^\]]+)\](.*)'
        match = re.match(regex, range_str)
        if match:
            prefix = match.group(1)
            range_part = match.group(2)
            suffix = match.group(3)
            range_items = re.split(r',', range_part)
            nodes = []
            for item in range_items:
                if '-' in item:
                    start_str, end_str = item.split('-')
                    width = max(len(start_str), len(end_str))
                    start = int(start_str)
                    end = int(end_str)
                    for i in range(start, end+1):
                        node = f"{prefix}{str(i).zfill(width)}{suffix}"
                        nodes.append(node)
                else:
                    node = f"{prefix}{item}{suffix}"
                    nodes.append(node)
            return nodes
        else:
            return [range_str]

    all_nodes = []
    parts = re.split(r',(?![^\[]*\])', nodelist)
    for part in parts:
        part = part.strip()
        if '[' in part and ']' in part:
            nodes = parse_range(part)
            all_nodes.extend(nodes)
        else:
            all_nodes.append(part)
    print(' '.join(all_nodes))

expand_nodelist(nodelist)
END
}

# Obtain the MASTER_NODE without using scontrol
if [ -n "$SLURM_NODELIST" ]; then
    HOSTLIST=$(expand_slurm_nodelist)
    echo "Expanded HOSTLIST: $HOSTLIST"
    MASTER_NODE=$(echo "$HOSTLIST" | awk '{print $1}')
    echo "Selected MASTER_NODE: $MASTER_NODE"
else
    MASTER_NODE=$(hostname)
    echo "Warning: Using current node as MASTER_NODE."
fi

# Convert hostname to IP address
MASTER_ADDR=$(getent hosts "$MASTER_NODE" | awk '{ print $1 }')
export MASTER_ADDR

echo "Master process IP: $MASTER_ADDR"

# Debugging: Check if MASTER_ADDR is set
if [ -z "$MASTER_ADDR" ]; then
    echo "Error: MASTER_ADDR is not set."
    exit 1
fi



# ------------------------------------------------------------------------------
# 3) Start Ray either as HEAD (if SLURM_NODEID=0) or WORKER
# ------------------------------------------------------------------------------
export RAY_PORT=6385

if [ "${SLURM_NODEID:-0}" = "0" ]; then
    echo "[Node 0] Starting Ray HEAD at IP $MASTER_ADDR"
    # Start Ray in background
    ray start --head \
              --node-ip-address="$MASTER_ADDR" \
              --port="$RAY_PORT" \
              --disable-usage-stats \
              --dashboard-port=8265 \
              --metrics-export-port=8080 \
              --object-manager-port=8076 \
              --node-manager-port=8077 \
              --ray-client-server-port=10001 \
              --dashboard-agent-listen-port=52365 \
              --dashboard-agent-grpc-port=52366 \
              --dashboard-grpc-port=52367 \
              --runtime-env-agent-port=52368 \
              --block &
    # Wait a few secs to ensure head is running
    sleep 10
else
    sleep 10
    echo "[Node ${SLURM_NODEID}] Connecting as worker to ${MASTER_ADDR}:${RAY_PORT}"
    ray start --address="${MASTER_ADDR}:${RAY_PORT}" \
              --disable-usage-stats \
              --dashboard-port=8265 \
              --metrics-export-port=8080 \
              --object-manager-port=8076 \
              --node-manager-port=8077 \
              --ray-client-server-port=10001 \
              --dashboard-agent-listen-port=52365 \
              --dashboard-agent-grpc-port=52366 \
              --dashboard-grpc-port=52367 \
              --runtime-env-agent-port=52368 \
              --block &

    sleep 10
fi




##### RL trianing script



temperature=1.1
lr=4e-6
# lr_warmup_steps_ratio=0.001
n_response=8
kl=0.0


batch_size=64
ppo_mini_batch_size=64
max_prompt_length=17408 # 17k
max_response_length=32768 # 32K
# add the max_prompt_length and max_response_length as the max_num_batched_tokens
max_num_batched_tokens=$((max_prompt_length + max_response_length))
EXP_NAME="14b_8nodes_train_48k_all_from_sft_extract_lr4e_6_t1_1"




DIR=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training # Set your dir
# swe_train_path_0=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/train_0.parquet
# swe_train_path_1=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/train_1.parquet
# swe_train_path_2=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/train_2.parquet
# swe_train_path_3=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/train_3.parquet
# swe_train_path_4=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/train_4.parquet
# swe_train_path_5=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train/train_5.parquet

swe_test_path=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl/train.parquet
swe_train_path_0=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_0.parquet
swe_train_path_1=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_1.parquet
swe_train_path_2=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_2.parquet
swe_train_path_3=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_3.parquet
swe_train_path_4=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_4.parquet
swe_train_path_5=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_5.parquet
swe_train_path_6=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_6.parquet
swe_train_path_7=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_7.parquet
swe_train_path_8=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_8.parquet
swe_train_path_9=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_9.parquet
swe_train_path_10=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_10.parquet
swe_train_path_11=/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/data/swe_rl_train_all/train_filter_11.parquet


train_files="[\"$swe_train_path_0\",\"$swe_train_path_1\",\"$swe_train_path_2\",\"$swe_train_path_3\",\"$swe_train_path_4\",\"$swe_train_path_5\",\"$swe_train_path_6\",\"$swe_train_path_7\",\"$swe_train_path_8\",\"$swe_train_path_9\",\"$swe_train_path_10\",\"$swe_train_path_11\"]"
test_files="[\"$swe_test_path\"]"

echo "[Node ${SLURM_NODEID}] Launching Python training script"

PROJECT_NAME='debug'

MODEL_CKPT_PATH=$DIR/verl/checkpoints/$PROJECT_NAME/$EXP_NAME/actor/
MODEL_PATH="/lustre/fsw/portfolios/llmservice/users/yangyic/Post-Training/verl/checkpoints/debug/14b_sft_repair_loc/global_step_2160"



python3 -m verl.trainer.main_ppo \
    algorithm.adv_estimator=grpo \
    data.train_files=$train_files \
    data.val_files=$test_files \
    data.train_batch_size=$batch_size \
    data.max_prompt_length=$max_prompt_length \
    data.max_response_length=$max_response_length \
    actor_rollout_ref.model.path=$MODEL_PATH \
    actor_rollout_ref.actor.optim.lr=$lr \
    actor_rollout_ref.model.use_remove_padding=True \
    actor_rollout_ref.rollout.temperature=${temperature} \
    actor_rollout_ref.actor.ppo_mini_batch_size=$ppo_mini_batch_size \
    actor_rollout_ref.actor.ppo_micro_batch_size_per_gpu=16 \
    actor_rollout_ref.actor.use_dynamic_bsz=True \
    actor_rollout_ref.actor.ppo_max_token_len_per_gpu=7000 \
    actor_rollout_ref.actor.use_kl_loss=True \
    actor_rollout_ref.actor.kl_loss_coef=$kl \
    actor_rollout_ref.actor.kl_loss_type=low_var_kl \
    actor_rollout_ref.actor.ulysses_sequence_parallel_size=8 \
    actor_rollout_ref.actor.entropy_coeff=0 \
    actor_rollout_ref.model.enable_gradient_checkpointing=True \
    actor_rollout_ref.actor.fsdp_config.param_offload=False \
    actor_rollout_ref.actor.fsdp_config.optimizer_offload=False \
    actor_rollout_ref.rollout.log_prob_micro_batch_size_per_gpu=32 \
    actor_rollout_ref.rollout.tensor_model_parallel_size=4 \
    actor_rollout_ref.rollout.name=vllm \
    actor_rollout_ref.rollout.gpu_memory_utilization=0.7 \
    actor_rollout_ref.rollout.n=$n_response \
    actor_rollout_ref.ref.log_prob_micro_batch_size_per_gpu=32 \
    actor_rollout_ref.ref.fsdp_config.param_offload=True \
    trainer.critic_warmup=0 \
    trainer.logger=['console','wandb'] \
    trainer.project_name=$PROJECT_NAME \
    trainer.experiment_name=$EXP_NAME \
    +trainer.val_before_train=False \
    trainer.n_gpus_per_node=8 \
    trainer.nnodes=8 \
    trainer.save_freq=12 \
    trainer.test_freq=30 \
    trainer.total_epochs=2 $@

# actor_rollout_ref.rollout.max_num_batched_tokens=$max_num_batched_tokens \

############### 



# temperature=1
# lr=1e-6
# n_response=8
# kl=0.0
# train_dataset=mixv7_full_qwen3 # set
# batch_size=128
# ppo_mini_batch_size=128
# max_response_length=8192
# EXP_NAME=8k

# DIR=/lustre/fsw/portfolios/llmservice/users/yachen/AceMath # Set your dir
# math_train_path=$DIR/data/${train_dataset}/train.parquet

# aime_test_path=$DIR/data/aime_2024_r1_8/test.parquet
# aime25_test_path=$DIR/data/aime_2025_r1_8/test.parquet

# train_files="['$math_train_path']"
# test_files="['$aime_test_path', '$aime25_test_path']"

# echo "[Node ${SLURM_NODEID}] Launching Python training script"

# PROJECT_NAME='grpo_qwen3_8b_test'
# MODEL_PATH="/lustre/fsw/portfolios/llmservice/users/yachen/AceMath/checkpoint/qwen3_8b"

# python3 -m verl.trainer.main_ppo \
#     algorithm.adv_estimator=grpo \
#     data.train_files="$train_files" \
#     data.val_files="$test_files" \
#     data.train_batch_size=$batch_size \
#     data.val_batch_size=512 \
#     data.max_prompt_length=1024 \
#     data.max_response_length=$max_response_length \
#     actor_rollout_ref.model.path=$MODEL_PATH \
#     actor_rollout_ref.actor.optim.lr=${lr} \
#     actor_rollout_ref.model.use_remove_padding=True \
#     actor_rollout_ref.actor.ppo_mini_batch_size=$ppo_mini_batch_size \
#     actor_rollout_ref.actor.use_dynamic_bsz=True \
#     actor_rollout_ref.actor.ppo_max_token_len_per_gpu=32768 \
#     actor_rollout_ref.actor.entropy_coeff=0 \
#     actor_rollout_ref.actor.use_kl_loss=True \
#     actor_rollout_ref.actor.kl_loss_coef=${kl} \
#     actor_rollout_ref.actor.kl_loss_type=low_var_kl \
#     actor_rollout_ref.actor.ulysses_sequence_parallel_size=2 \
#     actor_rollout_ref.model.enable_gradient_checkpointing=True \
#     actor_rollout_ref.actor.fsdp_config.param_offload=True \
#     actor_rollout_ref.actor.fsdp_config.optimizer_offload=True \
#     actor_rollout_ref.rollout.tensor_model_parallel_size=2 \
#     actor_rollout_ref.rollout.name=vllm \
#     actor_rollout_ref.rollout.temperature=${temperature} \
#     actor_rollout_ref.rollout.gpu_memory_utilization=0.6 \
#     actor_rollout_ref.rollout.n=${n_response} \
#     reward_model.reward_manager="prime" \
#     actor_rollout_ref.ref.fsdp_config.param_offload=True \
#     algorithm.kl_ctrl.kl_coef=${kl} \
#     +algorithm.mask_truncated_samples=True \
#     trainer.critic_warmup=0 \
#     trainer.logger=['console','wandb'] \
#     trainer.project_name=$PROJECT_NAME \
#     trainer.experiment_name=$EXP_NAME \
#     +trainer.val_before_train=False \
#     trainer.n_gpus_per_node=8 \
#     trainer.nnodes=2 \
#     trainer.save_freq=2 \
#     trainer.test_freq=-1 \
#     trainer.total_epochs=15 \
